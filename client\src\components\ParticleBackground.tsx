import { useEffect, useRef } from "react";
import { useParticleAnimation } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { PARTICLE_CONFIG, Z_INDEX } from '@/constants';

interface ParticleBackgroundProps {
  particleCount?: number;
  className?: string;
}

export default function ParticleBackground({
  particleCount = PARTICLE_CONFIG.count,
  className = "",
}: ParticleBackgroundProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const { createParticles, cleanup } = useParticleAnimation();
  const { handleError } = useErrorHandler();

  useEffect(() => {
    try {
      if (!containerRef.current) return;

      const accessibility = getAccessibilityConfig();

      // Reduce particle count for reduced motion or performance
      const adjustedCount = accessibility.reduceMotion
        ? Math.min(particleCount, 10)
        : particleCount;

      createParticles(containerRef.current, adjustedCount);
    } catch (error) {
      handleError(error as Error);
    }

    return cleanup;
  }, [createParticles, cleanup, particleCount, handleError]);

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 pointer-events-none ${className}`}
      style={{ zIndex: Z_INDEX.PARTICLES }}
      role="presentation"
      aria-hidden="true"
    />
  );
}
