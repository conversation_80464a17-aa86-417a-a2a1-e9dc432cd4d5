/**
 * Global type definitions for the portfolio website
 */

// GSAP types
export interface GSAPInstance {
  to: (target: any, vars: any) => any;
  from: (target: any, vars: any) => any;
  fromTo: (target: any, fromVars: any, toVars: any) => any;
  set: (target: any, vars: any) => any;
  timeline: (vars?: any) => GSAPTimeline;
  registerPlugin: (plugin: any) => void;
  killTweensOf: (target: any) => void;
}

export interface GSAPTimeline {
  to: (target: any, vars: any, position?: string | number) => GSAPTimeline;
  from: (target: any, vars: any, position?: string | number) => GSAPTimeline;
  fromTo: (target: any, fromVars: any, toVars: any, position?: string | number) => GSAPTimeline;
  set: (target: any, vars: any, position?: string | number) => GSAPTimeline;
  call: (callback: () => void, params?: any[], scope?: any, position?: string | number) => GSAPTimeline;
  onComplete?: () => void;
  repeat?: number;
  repeatDelay?: number;
}

export interface ScrollTriggerInstance {
  create: (vars: any) => any;
  refresh: () => void;
  update: () => void;
}

// Animation types
export interface AnimationConfig {
  duration?: number;
  delay?: number;
  ease?: string;
  repeat?: number;
  yoyo?: boolean;
}

export interface LetterAnimationConfig extends AnimationConfig {
  type: 'glow' | 'rotation' | 'bounce' | 'flip' | 'pulse';
  color?: string;
  scale?: number;
  rotation?: number;
}

// Component types
export interface ProjectData {
  id: number;
  title: string;
  description: string;
  type: string;
  placeholder: string;
  technologies?: string[];
  liveUrl?: string;
  githubUrl?: string;
  status: 'coming-soon' | 'live' | 'in-development';
}

export interface NavigationItem {
  label: string;
  href: string;
  color?: string;
}

export interface ParticleConfig {
  count: number;
  speed: number;
  opacity: number;
  size: number;
}

// Error types
export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: any;
}

// Performance types
export interface PerformanceMetrics {
  animationFrameRate: number;
  memoryUsage: number;
  renderTime: number;
}

// Accessibility types
export interface AccessibilityConfig {
  reduceMotion: boolean;
  highContrast: boolean;
  screenReader: boolean;
}

// Theme types
export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  foreground: string;
}

// Global window interface extensions
declare global {
  interface Window {
    gsap: GSAPInstance;
    ScrollTrigger: ScrollTriggerInstance;
  }
}
